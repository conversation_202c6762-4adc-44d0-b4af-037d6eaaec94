{% extends 'layouts/government.html' %}

{% block title %}{{ ordinance.title }} - PDF Viewer - Sangguniang Bayan Ordinance System{% endblock %}

{% block content %}
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'ordinances:home' %}" class="text-gray-700 hover:text-blue-600">
                    Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{% url 'ordinances:ordinance_list' %}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">
                        Ordinances
                    </a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ ordinance.get_absolute_url }}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">
                        {{ ordinance.ordinance_number }}
                    </a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-gray-500 md:ml-2">PDF Viewer</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="flex-1">
                <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ ordinance.title }}</h1>
                <div class="flex items-center gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-sm font-semibold px-3 py-1 rounded">
                        {{ ordinance.ordinance_number }}
                    </span>
                    <span class="bg-gray-100 text-gray-800 text-sm font-semibold px-3 py-1 rounded">
                        {{ ordinance.year_passed }}
                    </span>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex flex-col sm:flex-row gap-3 mt-4 lg:mt-0">
                <a href="{{ ordinance.get_absolute_url }}"
                   class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m0 7h18" />
                    </svg>
                    Back to Details
                </a>

                <button onclick="window.print()"
                        class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                    </svg>
                    Print
                </button>
            </div>
        </div>
    </div>

    <!-- Security Notice and Watermark -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-start">
            <svg class="h-5 w-5 text-red-400 mt-0.5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div class="flex-1">
                <h3 class="text-sm font-medium text-red-800 mb-1">Document Security Notice</h3>
                <div class="text-sm text-red-700">
                    <p class="mb-2"><strong>This document is protected.</strong> Unauthorized reproduction, screenshots, or distribution is prohibited and may be subject to legal action.</p>
                    <div class="bg-red-100 p-3 rounded border text-xs font-mono">
                        {% if user.is_authenticated %}
                            {{ user.username }} - {{ user.email }} - {{ security_context.user_ip }} - {{ security_context.access_time }} - Session: {{ security_context.session_id|slice:":8" }}
                        {% else %}
                            Guest - {{ security_context.user_ip }} - {{ security_context.access_time }} - Session: {{ security_context.session_id|slice:":8" }}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PDF Viewer Container -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-900">Official Document</h2>
                <div id="pdf-status" class="text-sm text-gray-600">
                    <span id="loading-indicator" class="inline-flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Loading PDF...
                    </span>
                    <span id="success-indicator" class="hidden text-green-600">✓ PDF Loaded</span>
                    <span id="error-indicator" class="hidden text-red-600">⚠ Loading Failed</span>
                </div>
            </div>
        </div>

        <div class="relative">
            <!-- PDF Embed with security measures -->
            <div id="pdf-container" class="w-full" style="height: 80vh;">
                <iframe
                    id="pdf-iframe"
                    src="{{ pdf_url }}#toolbar=0&navpanes=0&scrollbar=1&view=FitH&zoom=page-width"
                    width="100%"
                    height="100%"
                    style="border: none;"
                    oncontextmenu="return false;"
                    onload="addWatermark()"
                    onerror="handlePdfError()">
                    <p>Your browser does not support PDFs.
                       <a href="{{ pdf_url }}" target="_blank">View the PDF in a new tab</a>.
                    </p>
                </iframe>

                <!-- Alternative PDF viewer using object tag -->
                <div id="pdf-object-viewer" class="hidden w-full" style="height: 80vh;">
                    <object
                        data="{{ pdf_url }}"
                        type="application/pdf"
                        width="100%"
                        height="100%"
                        style="border: none;">
                        <p>Your browser does not support PDF viewing.
                           <a href="{{ pdf_url }}" target="_blank">View the PDF in a new tab</a>.
                        </p>
                    </object>
                </div>

                <!-- Fallback for browsers that don't support PDF viewing -->
                <div id="pdf-fallback" class="hidden p-8 text-center">
                    <div class="mb-4">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">PDF Viewer Not Available</h3>
                    <p class="text-gray-600 mb-4">Your browser doesn't support inline PDF viewing.</p>
                    <div class="space-y-3">
                        <a href="{{ pdf_url }}"
                           target="_blank"
                           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            Open PDF in New Tab
                        </a>
                        <br>
                        <button onclick="tryObjectViewer()"
                                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Try Alternative Viewer
                        </button>
                    </div>
                </div>
            </div>

            <!-- Overlay watermark -->
            <div id="watermark-overlay" class="absolute inset-0 pointer-events-none z-10" style="background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 100px,
                rgba(239, 68, 68, 0.05) 100px,
                rgba(239, 68, 68, 0.05) 120px
            );">
                <div class="absolute top-4 right-4 bg-red-600 text-white px-3 py-1 rounded text-xs font-bold opacity-75 shadow-lg">
                    CONFIDENTIAL
                </div>
                <div class="absolute bottom-4 left-4 bg-red-600 text-white px-3 py-1 rounded text-xs font-bold opacity-75 shadow-lg">
                    OFFICIAL DOCUMENT
                </div>

                <!-- Center watermark for extra visibility -->
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 -rotate-45 opacity-10 pointer-events-none">
                    <div class="text-red-600 text-6xl font-bold whitespace-nowrap">
                        CONFIDENTIAL DOCUMENT
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Security Information -->
    <div class="mt-6 bg-gray-50 rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-900 mb-2">Access Information</h3>
        <div class="text-xs text-gray-600 space-y-1">
            <p><strong>Document:</strong> {{ ordinance.ordinance_number }} - {{ ordinance.title }}</p>
            <p><strong>Accessed by:</strong>
                {% if user.is_authenticated %}
                    {{ user.get_full_name|default:user.username }} ({{ user.email }})
                {% else %}
                    Guest User
                {% endif %}
            </p>
            <p><strong>Access Time:</strong> {{ security_context.access_time }}</p>
            <p><strong>IP Address:</strong> {{ security_context.user_ip }}</p>
            <p><strong>Session ID:</strong> {{ security_context.session_id|slice:":8" }}...</p>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
    // Disable right-click context menu
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });

    // Disable text selection
    document.addEventListener('selectstart', function(e) {
        e.preventDefault();
        return false;
    });

    // Disable drag and drop
    document.addEventListener('dragstart', function(e) {
        e.preventDefault();
        return false;
    });

    // Disable certain keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U, Ctrl+S, Ctrl+A, Ctrl+P
        if (e.keyCode === 123 ||
            (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) ||
            (e.ctrlKey && (e.keyCode === 85 || e.keyCode === 83 || e.keyCode === 65))) {
            e.preventDefault();
            return false;
        }
    });

    // Update status indicators
    function updateStatus(status) {
        const loading = document.getElementById('loading-indicator');
        const success = document.getElementById('success-indicator');
        const error = document.getElementById('error-indicator');

        loading.classList.add('hidden');
        success.classList.add('hidden');
        error.classList.add('hidden');

        if (status === 'loading') {
            loading.classList.remove('hidden');
        } else if (status === 'success') {
            success.classList.remove('hidden');
        } else if (status === 'error') {
            error.classList.remove('hidden');
        }
    }

    // Add watermark function
    function addWatermark() {
        console.log('PDF loaded with security measures');
        updateStatus('success');
        // Check if PDF loaded successfully
        setTimeout(checkPdfLoaded, 1000);
    }

    // Handle PDF loading errors
    function handlePdfError() {
        console.log('PDF failed to load, showing fallback');
        updateStatus('error');
        showPdfFallback();
    }

    // Check if PDF loaded successfully
    function checkPdfLoaded() {
        const iframe = document.getElementById('pdf-iframe');
        try {
            // Try to access iframe content to see if PDF loaded
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (!iframeDoc || iframeDoc.body.innerHTML.includes('error') || iframeDoc.body.innerHTML.trim() === '') {
                showPdfFallback();
            }
        } catch (e) {
            // Cross-origin restrictions mean PDF is likely loaded correctly
            console.log('PDF appears to be loaded (cross-origin restrictions in place)');
        }
    }

    // Show PDF fallback
    function showPdfFallback() {
        document.getElementById('pdf-container').style.display = 'none';
        document.getElementById('pdf-fallback').classList.remove('hidden');
        document.getElementById('watermark-overlay').style.display = 'none';
        updateStatus('error');
    }

    // Try alternative object viewer
    function tryObjectViewer() {
        document.getElementById('pdf-fallback').classList.add('hidden');
        document.getElementById('pdf-object-viewer').classList.remove('hidden');
        document.getElementById('watermark-overlay').style.display = 'block';

        // Check if object viewer works after a delay
        setTimeout(function() {
            const objectViewer = document.getElementById('pdf-object-viewer');
            const objectElement = objectViewer.querySelector('object');
            if (objectElement && objectElement.offsetHeight === 0) {
                // Object viewer also failed, show fallback again
                document.getElementById('pdf-object-viewer').classList.add('hidden');
                document.getElementById('pdf-fallback').classList.remove('hidden');
                document.getElementById('watermark-overlay').style.display = 'none';
            }
        }, 2000);
    }

    // Prevent iframe manipulation
    window.addEventListener('load', function() {
        const iframe = document.querySelector('iframe');
        if (iframe) {
            iframe.style.pointerEvents = 'auto';
        }

        // Additional check for PDF loading after page load
        setTimeout(function() {
            const iframe = document.getElementById('pdf-iframe');
            if (iframe && iframe.offsetHeight === 0) {
                showPdfFallback();
            }
        }, 2000);
    });
</script>
{% endblock %}
