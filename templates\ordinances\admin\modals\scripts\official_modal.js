/**
 * Official Modal JavaScript
 * Handles the Add Official modal functionality
 */

// Official Modal Functions
window.openAddOfficialModal = function() {
    openModal('addOfficialModal', 'modalContent');
};

window.closeAddOfficialModal = function() {
    closeModal('addOfficialModal', 'modalContent', 'addOfficialForm', resetImagePreview);
};

// Image preview functionality
function initializeImagePreview() {
    const imagePreview = document.getElementById('imagePreview');
    const profilePictureInput = document.getElementById('profile_picture');

    if (imagePreview && profilePictureInput) {
        imagePreview.addEventListener('click', () => {
            profilePictureInput.click();
        });

        profilePictureInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreview.innerHTML = `
                        <img src="${e.target.result}" alt="Preview" class="w-full h-full object-cover rounded-lg">
                    `;
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

function resetImagePreview() {
    const imagePreview = document.getElementById('imagePreview');
    if (imagePreview) {
        imagePreview.innerHTML = `
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <p class="mt-2 text-sm text-gray-600">Click to upload photo</p>
                <p class="text-xs text-gray-500">PNG, JPG up to 5MB</p>
            </div>
        `;
    }
}

// Achievement management
window.addAchievement = function() {
    const container = document.getElementById('achievementsContainer');
    const newAchievement = document.createElement('div');
    newAchievement.className = 'flex items-center space-x-2 mb-2';
    newAchievement.innerHTML = `
        <input type="text" name="achievements[]"
               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
               placeholder="Enter an achievement">
        <button type="button" onclick="removeAchievement(this)"
                class="bg-red-500 text-white px-3 py-2 rounded-lg hover:bg-red-600 transition-colors">
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    `;
    container.appendChild(newAchievement);
};

window.removeAchievement = function(button) {
    button.parentElement.remove();
};

// Position-based committee suggestions
function initializePositionSuggestions() {
    const positionSelect = document.getElementById('position');
    const committeeInput = document.getElementById('committee');

    if (positionSelect && committeeInput) {
        positionSelect.addEventListener('change', function() {
            const position = this.value;
            let suggestion = '';

            switch(position) {
                case 'mayor':
                    suggestion = 'Office of the Municipal Mayor';
                    break;
                case 'vice_mayor':
                    suggestion = 'Office of the Vice Mayor';
                    break;
                case 'councilor':
                    suggestion = 'Committee on ';
                    break;
                case 'secretary':
                    suggestion = 'Municipal Secretary Office';
                    break;
                case 'treasurer':
                    suggestion = 'Municipal Treasurer Office';
                    break;
            }

            if (suggestion && !committeeInput.value) {
                committeeInput.value = suggestion;
            }
        });
    }
}

// Initialize official modal functionality
function initializeOfficialModal() {
    // Setup click outside to close
    setupClickOutsideClose('addOfficialModal', closeAddOfficialModal);

    // Initialize image preview
    initializeImagePreview();

    // Initialize position suggestions
    initializePositionSuggestions();

    // Setup form submission
    handleFormSubmission(
        'addOfficialForm',
        '/manage/officials/create-ajax/',
        'Official created successfully!',
        closeAddOfficialModal
    );
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeOfficialModal();
});
