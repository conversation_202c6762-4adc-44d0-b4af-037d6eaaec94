# PDF Viewer Troubleshooting Guide

## 🔍 **Common Issues and Solutions**

### **Issue 1: PDF Not Displaying in Iframe**

**Symptoms:**
- Blank iframe or loading indicator stuck
- Error message "PDF cannot be displayed"
- <PERSON><PERSON><PERSON> shows "This page can't be displayed"

**Possible Causes & Solutions:**

#### **A. Browser PDF Plugin Issues**
- **Chrome/Edge**: Built-in PDF viewer should work
- **Firefox**: May need PDF.js enabled
- **Safari**: Should work with built-in viewer
- **Solution**: Try the "Try PDF.js Viewer" button in the fallback options

#### **B. CORS (Cross-Origin) Issues**
- **Cause**: PDF served from different domain/port
- **Solution**: Ensure PDF is served from same domain as the application
- **Check**: Look for CORS errors in browser console (F12)

#### **C. Content-Type Issues**
- **Cause**: PDF not served with correct MIME type
- **Solution**: Ensure Django serves PDFs with `application/pdf` content type
- **Test**: Click "Test Direct PDF Access" button to check

### **Issue 2: PDF File Not Found**

**Symptoms:**
- 404 error when accessing PDF URL
- "No PDF file available" message

**Solutions:**
1. **Check File Exists**: Verify PDF file exists in media directory
2. **Check Media Settings**: Ensure `MEDIA_URL` and `MEDIA_ROOT` configured correctly
3. **Check File Permissions**: Ensure web server can read PDF files

### **Issue 3: Browser Compatibility**

**Different browsers handle PDFs differently:**

| Browser | PDF Support | Notes |
|---------|-------------|-------|
| Chrome | ✅ Excellent | Built-in PDF viewer |
| Firefox | ✅ Good | Uses PDF.js |
| Safari | ✅ Good | Built-in viewer |
| Edge | ✅ Excellent | Built-in PDF viewer |
| Mobile | ⚠️ Variable | May need fallbacks |

### **Issue 4: Security Restrictions**

**Some browsers block PDF embedding for security:**
- **Solution**: Use the alternative viewers provided
- **Fallback**: Direct PDF link in new tab

## 🛠️ **Debugging Steps**

### **Step 1: Check Debug Information**
The PDF viewer shows debug info including:
- PDF URL (click to test direct access)
- File path and size
- Browser type
- PDF support detection

### **Step 2: Test Direct PDF Access**
1. Click "Test Direct PDF Access" button
2. Check browser console for errors
3. Verify PDF opens in new tab when clicking PDF URL

### **Step 3: Try Alternative Viewers**
If main iframe doesn't work:
1. Click "Try Object Viewer" - uses HTML `<object>` tag
2. Click "Try PDF.js Viewer" - uses Mozilla's PDF.js

### **Step 4: Check Browser Console**
1. Press F12 to open developer tools
2. Go to Console tab
3. Look for errors related to PDF loading

## 🔧 **Technical Solutions**

### **For Developers:**

#### **1. Ensure Proper Media Configuration**
```python
# settings.py
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# urls.py (in development)
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
```

#### **2. Check PDF File Validation**
```python
# models.py
def validate_pdf_file(value):
    if value:
        ext = os.path.splitext(value.name)[1].lower()
        if ext != '.pdf':
            raise ValidationError('Only PDF files are allowed.')
```

#### **3. Verify PDF Content-Type**
The server should serve PDFs with `Content-Type: application/pdf`

### **For Users:**

#### **1. Browser Settings**
- Ensure PDF viewing is enabled in browser settings
- Check if PDF plugins are disabled
- Try different browser if issues persist

#### **2. Clear Browser Cache**
- Clear browser cache and cookies
- Try incognito/private browsing mode

#### **3. Check Internet Connection**
- Ensure stable internet connection
- Try refreshing the page

## 📱 **Mobile Device Issues**

**Common mobile issues:**
- iOS Safari may not display PDFs in iframe
- Android browsers vary in PDF support
- Small screen size affects viewing

**Solutions:**
- Use PDF.js viewer for better mobile compatibility
- Provide direct download option
- Optimize PDF for mobile viewing

## 🚨 **Emergency Fallbacks**

If all viewers fail:
1. **Direct PDF Link**: Click PDF URL to open in new tab
2. **Download Option**: Right-click PDF URL and save
3. **Alternative Browser**: Try different browser
4. **PDF Reader App**: Download and open in dedicated PDF app

## 📞 **Getting Help**

If issues persist:
1. **Check Browser Console**: Look for specific error messages
2. **Test with Different PDFs**: Try with different PDF files
3. **Test Different Browsers**: Verify if issue is browser-specific
4. **Check Server Logs**: Look for server-side errors

## ✅ **Quick Checklist**

- [ ] PDF file exists and is accessible
- [ ] Browser supports PDF viewing
- [ ] No CORS errors in console
- [ ] Media files properly configured
- [ ] PDF served with correct content-type
- [ ] Alternative viewers tested
- [ ] Browser cache cleared
- [ ] Different browser tested

## 🎯 **Most Common Solution**

**90% of PDF viewing issues are resolved by:**
1. Using the PDF.js viewer option
2. Opening PDF in new tab
3. Trying a different browser

The secure PDF viewer provides multiple fallback options to ensure PDFs can be viewed regardless of browser limitations.
